// Fill the DES unemployment form automatically
function fillForm() {
    // Set your details here
    const details = {
        subject: "Unemployment Call Center Problem", // Change as needed
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        ssn_last4: "7321",
        confirmation_number: "7448243",
        email: "<EMAIL>",
        comments: `I filed my unemployment appeal 9 weeks ago, but after contacting the appellate office yesterday, I was informed that no file has even been created and no case number has been assigned. As a result, my case remains under the jurisdiction of DES and can still be resolved internally.\n\nThe original denial stated there was a lack of medical documentation. However, no one from DES ever contacted me to request additional documentation. If they had, I would have provided it immediately. I have now taken the initiative to log into Banner University Medical Center's patient portal and have obtained the necessary medical records confirming my condition at the time I left my job.\n\nTo clarify: I was diagnosed with acute Deep Vein Thrombosis (DVT) in my right leg on October 24, 2024. The ultrasound confirmed:\n\nA partially blocked vein behind my knee (popliteal vein)\n\nTwo fully blocked veins in my lower leg (gastrocnemius and peroneal veins)\n\nAt the time, I didn't know I had DVT—I thought it was just a pulled muscle. But the pain became so severe I could no longer walk or safely perform my duties at Amazon. Despite Amazon's own policies requiring employees to rotate between tasks to prevent strain, I was repeatedly placed in the most physically demanding roles—Water Spider and NonCon line loader—sometimes back-to-back or for multiple days in a row. Amazon even scanned me into other positions in the system to make it appear they were complying, while still assigning me to these strenuous tasks.\n\nI requested lighter-duty assignments to allow my leg to recover, but was told no accommodations were available due to staffing shortages. Very shortly afterward, I became completely unable to stand or walk.\n\nThis was not a voluntary quit. I was physically unable to continue working, and my condition has now been medically confirmed as both serious and dangerous. Due to financial hardship, I have remained mostly untreated, but I now have documentation that clearly validates my reason for separation from employment.\n\nGiven that:\n\nMy appeal has not been processed or assigned a case number;\n\nMy case is still under DES authority;\n\nDES failed to follow up for documentation prior to denial;\n\nI now have and am prepared to submit complete medical records;\n\nI respectfully request that my claim be reconsidered and resolved at this stage by DES.\n\nThank you for your time and help.`
    };

    // Fill dropdown
    const subjectSelect = document.querySelector('select[name="submitted[subject]"]');
    if (subjectSelect) {
        subjectSelect.value = details.subject;
        subjectSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }
    // Fill text fields
    const fillField = (selector, value) => {
        const el = document.querySelector(selector);
        if (el) {
            el.value = value;
            el.dispatchEvent(new Event('input', { bubbles: true }));
        }
    };
    fillField('input[name="submitted[first_name]"]', details.first_name);
    fillField('input[name="submitted[last_name]"]', details.last_name);
    fillField('input[name="submitted[last_four_digits_of_your__social_security_number]"]', details.ssn_last4);
    fillField('input[name="submitted[confirmation_number__if_you_filed_via_the_internet]"]', details.confirmation_number);
    fillField('input[name="submitted[email_address]"]', details.email);
    fillField('textarea[name="submitted[comments]"]', details.comments);
}

// Auto-fill form when page loads
function autoFillOnLoad() {
    // Wait for page to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(fillForm, 1000); // 1 second delay to ensure all elements are loaded
        });
    } else {
        // Page already loaded
        setTimeout(fillForm, 1000);
    }
}

// Listen for messages from the popup or background to trigger autofill (kept for manual control)
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
    if (msg === "fill_des_form") {
        fillForm();
        sendResponse({ status: "filled" });
    }
});

// Automatically fill form when script loads
autoFillOnLoad();
