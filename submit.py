import asyncio
from playwright.async_api import async_playwright
import time

async def fill_form(page, dropdown_option):
    """Fill out the form with the specified dropdown option"""
    
    # Navigate to the page
    await page.goto("https://des.az.gov/services/employment/unemployment-individual/contact-AZUI/client-advocate")
    
    # Wait for page to load
    await page.wait_for_load_state("networkidle")

    # Wait for user to manually solve Cloudflare challenge
    print("Waiting for you to solve any Cloudflare or CAPTCHA challenge in this tab...")
    print("Once the form appears, automation will continue.")
    await page.wait_for_selector('select[name="submitted[subject]"]', timeout=0)  # Wait indefinitely

    # Select dropdown option with robust error handling
    try:
        # Wait up to 60 seconds for the dropdown to appear
        await page.wait_for_selector('select[name="submitted[subject]"]', timeout=60000)
        await page.select_option('select[name="submitted[subject]"]', dropdown_option)
    except Exception as e:
        print(f"Dropdown not found or could not select option: {e}")
        content = await page.content()
        with open("debug_page.html", "w", encoding="utf-8") as f:
            f.write(content)
        print("Page content saved to debug_page.html for inspection.")
        return  # Skip to next form or handle as needed
    
    # Fill in First Name
    await page.fill('input[name="submitted[first_name]"]', "Cody")
    
    # Fill in Last Name
    await page.fill('input[name="submitted[last_name]"]', "Houser")
    
    # Fill in Last 4 digits of SSN
    await page.fill('input[name="submitted[last_four_digits_of_your__social_security_number]"]', "7321")
    
    # Fill in Confirmation Number
    await page.fill('input[name="submitted[confirmation_number__if_you_filed_via_the_internet]"]', "7448243")
    
    # Fill in Email
    await page.fill('input[name="submitted[email_address]"]', "<EMAIL>")
    
    # Fill in Comments
    comments = """I filed my unemployment appeal 9 weeks ago, but after contacting the appellate office yesterday, I was informed that no file has even been created and no case number has been assigned. As a result, my case remains under the jurisdiction of DES and can still be resolved internally.

The original denial stated there was a lack of medical documentation. However, no one from DES ever contacted me to request additional documentation. If they had, I would have provided it immediately. I have now taken the initiative to log into Banner University Medical Center's patient portal and have obtained the necessary medical records confirming my condition at the time I left my job.

To clarify: I was diagnosed with acute Deep Vein Thrombosis (DVT) in my right leg on October 24, 2024. The ultrasound confirmed:

A partially blocked vein behind my knee (popliteal vein)

Two fully blocked veins in my lower leg (gastrocnemius and peroneal veins)

At the time, I didn't know I had DVT—I thought it was just a pulled muscle. But the pain became so severe I could no longer walk or safely perform my duties at Amazon. Despite Amazon's own policies requiring employees to rotate between tasks to prevent strain, I was repeatedly placed in the most physically demanding roles—Water Spider and NonCon line loader—sometimes back-to-back or for multiple days in a row. Amazon even scanned me into other positions in the system to make it appear they were complying, while still assigning me to these strenuous tasks.

I requested lighter-duty assignments to allow my leg to recover, but was told no accommodations were available due to staffing shortages. Very shortly afterward, I became completely unable to stand or walk.

This was not a voluntary quit. I was physically unable to continue working, and my condition has now been medically confirmed as both serious and dangerous. Due to financial hardship, I have remained mostly untreated, but I now have documentation that clearly validates my reason for separation from employment.

Given that:

My appeal has not been processed or assigned a case number;

My case is still under DES authority;

DES failed to follow up for documentation prior to denial;

I now have and am prepared to submit complete medical records;

I respectfully request that my claim be reconsidered and resolved at this stage by DES.

Thank you for your time and help."""
    
    await page.fill('textarea[name="submitted[comments]"]', comments)
    
    # Wait for reCAPTCHA to load
    await page.wait_for_selector('.g-recaptcha')
    
    print(f"Form filled for option: {dropdown_option}")
    print("Please solve the captcha and submit the form manually in this tab.")
    print("The script will continue to the next form after a brief pause.")

async def main():
    # Dropdown options to cycle through
    dropdown_options = [
        "Unemployment Call Center Problem",
        "Disagree with Disqualification", 
        "Disagree with Overpayment Decision",
        "Disagree with Appeal Decision",
        "Request for Claim Documentation",
        "General Unemployment Complaint"
    ]
    
    import os
    import random
    from pathlib import Path
    import sys
    # --- Check if Chrome or Edge is running ---
    try:
        import psutil
    except ImportError:
        print("[!] psutil not found. Installing...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        import psutil

    def is_browser_running():
        browser_names = ["chrome.exe", "msedge.exe", "chrome", "msedge"]
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] and proc.info['name'].lower() in browser_names:
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False

    def kill_browser_processes():
        browser_names = ["chrome.exe", "msedge.exe", "chrome", "msedge"]
        killed = 0
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] and proc.info['name'].lower() in browser_names:
                    proc.kill()
                    killed += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return killed

    if is_browser_running():
        print("[!] Chrome or Edge is running. Please close ALL browser windows and background processes before running this script.")
        input("Press Enter after you have closed all browser windows...")
        if is_browser_running():
            print("[!] Chrome or Edge is still running.")
            resp = input("Would you like me to automatically kill all Chrome/Edge processes for you? [y/N]: ").strip().lower()
            if resp == 'y':
                killed = kill_browser_processes()
                if killed:
                    print(f"[i] Killed {killed} Chrome/Edge processes. Continuing...")
                else:
                    print("[!] No processes killed, but some may still be running. Exiting for your safety.")
                    sys.exit(1)
                if is_browser_running():
                    print("[!] Chrome or Edge is STILL running after attempted kill. Exiting for your safety.")
                    sys.exit(1)
            else:
                print("[!] Exiting for your safety.")
                sys.exit(1)

    async with async_playwright() as p:
        # === IMPORTANT ===
        # Using persistent Chrome/Edge profiles with Playwright on Windows causes decryption errors and browser shutdown.
        # We use a normal Playwright context for maximum compatibility and stability.
        # You will NOT be logged in to any Chrome/Edge accounts or have saved cookies, but automation will work reliably.

        realistic_user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/124.0.0.0 Safari/537.36"
        )

        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context(
            viewport={"width": 1280, "height": 900},
            user_agent=realistic_user_agent,
        )

        # === 3. Increase and randomize delays ===
        # Keep track of all pages/tabs
        pages = []
        
        # Loop through each dropdown option
        for i, option in enumerate(dropdown_options):
            print(f"\n--- Processing form {i+1}/6: {option} ---")
            
            # Create new page/tab
            page = await context.new_page()
            pages.append(page)
            
            # Fill the form
            await fill_form(page, option)
            
            # Give user time to solve captcha if needed
            if i < len(dropdown_options) - 1:  # Don't wait after the last form
                delay = random.randint(45, 75)  # 45-75 seconds between tabs
                print(f"Waiting {delay} seconds before opening next tab...")
                await asyncio.sleep(delay)
        
        print(f"\n--- All {len(dropdown_options)} forms have been filled ---")
        print("All tabs are now open. Please solve the captchas and submit each form manually.")
        print("The browser will remain open. Close it manually when you're done.")
        
        # Keep the browser open indefinitely
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\nScript interrupted by user. Closing browser...")
            await browser.close()


if __name__ == "__main__":
    asyncio.run(main())