function showMessage(text, isError = false) {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = text;
    messageDiv.className = isError ? 'error' : 'success';
    messageDiv.style.display = 'block';
}

function hideMessage() {
    const messageDiv = document.getElementById('message');
    messageDiv.style.display = 'none';
}

document.getElementById('fillBtn').addEventListener('click', function() {
    hideMessage();

    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
        const currentTab = tabs[0];
        const currentUrl = currentTab.url;

        // Check if we're on the correct DES form page
        if (!currentUrl.includes('des.az.gov/services/employment/unemployment-individual/contact-AZUI/client-advocate')) {
            showMessage('Please navigate to the DES client advocate form page first.', true);
            return;
        }

        // Send message to content script
        chrome.tabs.sendMessage(currentTab.id, "fill_des_form", function(response) {
            // Check for connection errors
            if (chrome.runtime.lastError) {
                console.error('Extension error:', chrome.runtime.lastError.message);
                showMessage('Error: Content script not ready. Please refresh the page and try again.', true);
                return;
            }

            if (response && response.status === "filled") {
                showMessage('Form filled successfully!');
                setTimeout(() => window.close(), 1500);
            } else {
                showMessage('Failed to fill form. Please try again.', true);
            }
        });
    });
});
